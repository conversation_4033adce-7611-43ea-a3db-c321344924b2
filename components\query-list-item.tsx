"use client"

import type { Query } from "@/types"
import { formatRelativeTime } from "@/lib/utils"
import { MessageSquareText } from "lucide-react"

interface QueryListItemProps {
  query: Query
  isActive: boolean
  onClick: () => void
}

export function QueryListItem({ query, isActive, onClick }: QueryListItemProps) {
  return (
    <button
      onClick={onClick}
      className={`w-full text-left p-3 rounded-lg transition-colors flex items-start gap-3
                  ${isActive ? "bg-teal-50" : "hover:bg-gray-100"}`}
    >
      <MessageSquareText className="h-5 w-5 text-gray-500 mt-0.5 flex-shrink-0" />
      <div className="flex-1">
        <h3 className="text-sm font-medium text-gray-800 truncate">{query.title}</h3>
        <p className="text-xs text-gray-500 mt-1">{formatRelativeTime(`${query.createdAt}`)}</p>
      </div>
    </button>
  )
}

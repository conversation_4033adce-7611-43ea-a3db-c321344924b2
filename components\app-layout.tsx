"use client"

import { useDispatch, useSelector } from "react-redux"
import { TabNavigation } from "./tab-navigation"
import { ChatInterface } from "./chat-interface"
import { QueriesView } from "./queries-view"
import type { RootState } from "@/store"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Plus } from "lucide-react"
import { usePathname, useRouter } from "next/navigation"

export function AppLayout() {
  const router = useRouter()
  const pathname = usePathname()
  const activeTab = useSelector((state: RootState) => state.ui.activeTab)
  const handleNewQuery = () => {
    router.push("/")
  }

  return (
    <div className="h-screen flex flex-col w-full bg-gradient-to-br from-indigo-50 via-white to-cyan-50 text-gray-900 px-px scrollbar">
      {/* Header with more spacing */}
      <div className="container mx-auto px-6 py-2">
        <header className="flex items-center justify-between">
          <TabNavigation />
          <div className="flex items-center gap-6">
            { pathname !== "/" && (
            <Button
              onClick={handleNewQuery}
              variant="outline"
              size="sm"
              className="flex items-center gap-2 hover:bg-gray-50"
            >
              <Plus className="h-4 w-4" />
              New Query
            </Button>
          )}
          <span className="text-sm font-medium text-gray-500">Sources</span>
          </div>
        </header>
      </div>

      {/* Main content with generous spacing */}
      <main className="flex-grow container mx-auto px-6 pb-8 flex flex-col overflow-hidden">
        {activeTab === "chat" && <ChatInterface />}
        {activeTab === "queries" && <QueriesView />}
      </main>
    </div>
  )
}

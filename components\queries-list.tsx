"use client"

import { useSelector, useDispatch } from "react-redux"
import { useRouter } from "next/navigation" // Import useRouter
import type { RootState, AppDispatch } from "@/store"
// setActiveQueryId from queriesSlice will be handled by the dynamic page
// loadMessages from chatSlice will be handled by the dynamic page
// setChatView from uiSlice will be handled by the dynamic page
// setActiveTab from uiSlice will be handled by the dynamic page
import { QueryListItem } from "./query-list-item"

export function QueriesList() {
  const dispatch = useDispatch<AppDispatch>()
  const router = useRouter() // Initialize router
  const { queries, activeQueryId } = useSelector((state: RootState) => state.queries)

  const handleQuerySelect = (queryId: string) => {
    // Navigate to the dynamic route for this query
    router.push(`/queries/${queryId}`)
    // The [id]/page.tsx component will handle dispatching actions
    // to load the query and set the correct UI states.
  }

  if (!queries || queries.length === 0) {
    return <p className="text-gray-500 text-sm">No past conversations found.</p>
  }

  return (
    <div className="space-y-1">
      {queries.map((query) => (
        <QueryListItem
          key={query.id}
          query={query}
          isActive={query.id === activeQueryId}
          onClick={() => handleQuerySelect(query.id)}
        />
      ))}
    </div>
  )
}

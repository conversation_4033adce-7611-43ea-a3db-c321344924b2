"use client"

import { useEffect, useRef } from "react"
import { useSelector, useDispatch } from "react-redux"
import { useRouter } from "next/navigation"
import type { RootState, AppDispatch } from "@/store"
import { setInputValue, processUserMessage, clearInputValue } from "@/store/chatSlice"
import { setActiveQueryId } from "@/store/queriesSlice"
import { setActiveTab, setChatView } from "@/store/uiSlice"

import { ChatHeader } from "./chat-header"
import { SourcesPanel } from "./sources-panel"
import { ChatInputSection } from "./chat-input-section"
import { MessageHistory } from "./message-history"
import { Button } from "@/components/ui/button"
import { Plus, MessageSquare } from "lucide-react"

const initialSuggestedQueries = [
  "Show me all customer queries from last 24 hours",
  "Search NPT-thread flow control valves",
  "More triggers can be shown here",
  "Many more triggers can be shown here",
]

const relatedTriggers = ["What are the alternatives?", "Can you provide more details on pricing?"]

export function ChatInterface() {
  const dispatch = useDispatch<AppDispatch>()
  const router = useRouter()
  const messagesEndRef = useRef<null | HTMLDivElement>(null)

  const inputValue = useSelector((state: RootState) => state.chat.inputValue)
  const { activeQueryId, queries } = useSelector((state: RootState) => state.queries)
  const activeQuery = queries.find((q) => q.id === activeQueryId)
  const currentMessages = activeQuery?.messages || []

  const isProcessing = currentMessages.some(
    (msg) => msg.type === "assistant" && msg.thinkingState?.status === "processing",
  )

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }

  useEffect(scrollToBottom, [currentMessages])

  const handleSend = async () => {
    if (!inputValue.trim()) return
    const resultAction = await dispatch(processUserMessage(inputValue))
    if (processUserMessage.fulfilled.match(resultAction)) {
      const { queryId, isNewQuery } = resultAction.payload
      if (isNewQuery) {
        router.push(`/queries/${queryId}`)
      }
    }
    dispatch(clearInputValue())
  }

  const handleSuggestedClick = (query: string) => {
    dispatch(setInputValue(query))
  }

  const handleNewQuery = () => {
    dispatch(setActiveQueryId(null))
    dispatch(setActiveTab("chat"))
    dispatch(setChatView("chat"))
    router.push("/")
  }

  const showInitialChatScreen = !activeQueryId && currentMessages.length === 0 && !isProcessing

  return (
    <div className="flex flex-col h-full">
      {showInitialChatScreen ? (
        <div className="flex-grow flex flex-col justify-center space-y-12">
          <ChatHeader username="James" />
          <SourcesPanel />
        </div>
      ) : (
        activeQuery && (
          <div className="flex-grow overflow-y-auto space-y-8 scrollbar">
            <MessageHistory messages={currentMessages} />
            <div ref={messagesEndRef} />
          </div>
        )
      )}

      {/* Input section with more spacing */}
      <div className="mt-auto pt-px">
        <ChatInputSection
          inputValue={inputValue}
          onInputChange={(val) => dispatch(setInputValue(val))}
          onSend={handleSend}
          onSuggestedClick={handleSuggestedClick}
          placeholder={
            isProcessing
              ? "Agent is thinking..."
              : activeQueryId
                ? "Ask a follow-up question..."
                : "Ask anything about Ross Products, Sales Processes, or Technical Specs"
          }
          suggestedQueries={activeQueryId ? relatedTriggers : initialSuggestedQueries}
          relatedTriggers={!!activeQueryId}
          disabled={isProcessing}
        />
      </div>
    </div>
  )
}

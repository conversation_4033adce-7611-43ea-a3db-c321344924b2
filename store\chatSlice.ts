import { createSlice, type PayloadAction, createAsyncThunk } from "@reduxjs/toolkit"
import type { Message, Query } from "@/types"
import { v4 as uuidv4 } from "uuid"
import type { RootState } from "./index"

// Removed mock data - now using real WebSocket data

interface ChatState {
  inputValue: string
  currentQueryText: string | null // Keep for UI display convenience
  currentMessages: Message[] // Keep for UI display convenience
  // WebSocket-related state
  currentMessageId: string | null // Track message being processed
  thinkingSteps: {
    [agentName: string]: {
      status: 'idle' | 'in-progress' | 'completed' | 'error'
      message: string
      result: any
      displayName: string
      error?: string
    }
  }
  finalResponse: {
    content: string
    timestamp: string
  } | null
  processingError: string | null
}

const initialState: ChatState = {
  inputValue: "",
  currentQueryText: null,
  currentMessages: [],
  // WebSocket-related initial state
  currentMessageId: null,
  thinkingSteps: {},
  finalResponse: null,
  processingError: null,
}

export const processUserMessage = createAsyncThunk(
  "chat/processUserMessage",
  async (userMessageContent: string, { dispatch, getState }) => {
    const {
      addQuery,
      addMessageToQuery,
      setActiveQueryId: setGlobalActiveQueryId,
    } = await import("./queriesSlice")
    const { setChatView } = await import("./uiSlice")

    const state = getState() as RootState
    let currentActiveQueryId = state.queries.activeQueryId
    const isNewQuery = !currentActiveQueryId

    const userMessageId = uuidv4()
    const userMessage: Message = {
      id: userMessageId,
      type: "user",
      content: userMessageContent,
      timestamp: `${new Date()}`,
    }

    // If it's a new query, create it first
    if (isNewQuery) {
      currentActiveQueryId = uuidv4()
      const newQuery: Query = {
        id: currentActiveQueryId,
        title: userMessageContent.length > 40 ? userMessageContent.substring(0, 37) + "..." : userMessageContent,
        messages: [userMessage],
        createdAt: new Date(),
      }
      dispatch(addQuery(newQuery))
      dispatch(setGlobalActiveQueryId(currentActiveQueryId))
      dispatch(loadMessages(newQuery.messages))
      dispatch(setCurrentQueryText(newQuery.title))
    } else {
      // Add user message to existing query
      dispatch(addMessageToQuery({ queryId: currentActiveQueryId as string, message: userMessage }))
      // Reload messages in chatSlice to include the new user message
      const updatedQuery = (getState() as RootState).queries.queries.find((q) => q.id === currentActiveQueryId)
      if (updatedQuery) {
        dispatch(loadMessages(updatedQuery.messages))
        dispatch(setCurrentQueryText(updatedQuery.title))
      }
    }

    // Set UI to thinking state - WebSocket will handle the actual processing
    dispatch(setChatView("thinking"))

    return { queryId: currentActiveQueryId, isNewQuery }
  },
)

const chatSlice = createSlice({
  name: "chat",
  initialState,
  reducers: {
    setInputValue(state, action: PayloadAction<string>) {
      state.inputValue = action.payload
    },
    loadMessages(state, action: PayloadAction<Message[]>) {
      state.currentMessages = action.payload
      // Set currentQueryText from the first user message
      const firstUserMessage = action.payload.find((msg) => msg.type === "user")
      state.currentQueryText = firstUserMessage?.content || null
    },
    setCurrentQueryText(state, action: PayloadAction<string | null>) {
      state.currentQueryText = action.payload
    },
    clearInputValue(state) {
      state.inputValue = ""
    },
    startNewChat(state) {
      state.inputValue = ""
      state.currentQueryText = null
      state.currentMessages = []
      state.currentMessageId = null
      state.thinkingSteps = {}
      state.finalResponse = null
      state.processingError = null
    },
    // WebSocket-related actions
    updateThinkingStep(state, action: PayloadAction<{
      agentName:  'user_intent_agent' | 'web_search_agent' | 'product_unstructured_agent' | 'database_search_agent' | 'feedback_agent' | 'supervisor_agent'
      status: 'idle' | 'in-progress' | 'completed' | 'error'
      message: string
      result: any
      displayName: string
      error?: string
    }>) {
      const { agentName, status, message, result, displayName, error } = action.payload
      state.thinkingSteps[agentName] = {
        status,
        message,
        result,
        displayName,
        error
      }
    },
    setFinalResponse(state, action: PayloadAction<{
      content: string
      timestamp: string
    }>) {
      state.finalResponse = action.payload
    },
    setProcessingError(state, action: PayloadAction<string>) {
      state.processingError = action.payload
    },
    resetThinkingSteps(state) {
      state.thinkingSteps = {}
      state.finalResponse = null
      state.processingError = null
    },
    setCurrentMessageId(state, action: PayloadAction<string | null>) {
      state.currentMessageId = action.payload
    },
  },
})

export const {
  setInputValue,
  loadMessages,
  setCurrentQueryText,
  clearInputValue,
  startNewChat,
  updateThinkingStep,
  setFinalResponse,
  setProcessingError,
  resetThinkingSteps,
  setCurrentMessageId
} = chatSlice.actions

export default chatSlice.reducer

import { createSlice, type PayloadAction, createAsyncThunk } from "@reduxjs/toolkit"
import type { ThinkingStep, Message, Query, MessageThinkingState, SourceInfo } from "@/types"
import { v4 as uuidv4 } from "uuid"
import type { RootState } from "./index"

// Initial thinking steps structure
const getInitialThinkingSteps = (): ThinkingStep[] => [
  { id: "ts-1", description: "Analyzing user query", status: "pending", details: "Understanding intent and entities." },
  { id: "ts-2", description: "Searching Web", status: "pending", details: "Gathering public information." },
  { id: "ts-3", description: "Searching Ross Database", status: "pending", details: "Checking internal product data." },
  {
    id: "ts-4",
    description: "Searching Ross Emails, PDFs, Docs",
    status: "pending",
    details: "Looking through documents.",
  },
  {
    id: "ts-5",
    description: "Searching Ross Senior Team Feedbacks",
    status: "pending",
    details: "Consulting expert opinions.",
  },
]
const mockMarkdown = `
# Sample Markdown

Welcome to the **React Markdown Demo**!

## Features

- Render headings, lists, and more
- **Bold** and _italic_ text
- [Links](https://reactjs.org)
- Inline \`code\` and code blocks

\`\`\`js
// Example code block
function greet() {
  console.log("Hello, Markdown!");
}
\`\`\`

> This is a blockquote.

| Column 1 | Column 2 |
|----------|----------|
| Row 1    | Data     |
| Row 2    | More     |

- [x] Task 1
- [ ] Task 2

Enjoy using markdown in your React app!
`;

// Mock final response data
const mockFinalResponse = {
  content: mockMarkdown,
  responseTitle: "Recommendation: Series 500 Flow Control Valves",
  sources: [
    {
      id: "src-db",
      name: "Ross Product DB - Series 500",
      type: "database",
      enabled: true,
      snippet: "Series 500, NPT, Brass, 300 PSI...",
    },
    {
      id: "src-pdf",
      name: "Tech Spec Sheet - FC500.pdf",
      type: "pdf",
      enabled: true,
      snippet: "Flow coefficient (Cv): 0.5-2.0",
    },
  ] as SourceInfo[],
}

interface ChatState {
  inputValue: string
  currentQueryText: string | null // Keep for UI display convenience
  currentMessages: Message[] // Keep for UI display convenience
  // WebSocket-related state
  currentMessageId: string | null // Track message being processed
  thinkingSteps: {
    [agentName: string]: {
      status: 'idle' | 'in-progress' | 'completed'
      message: string
      result: any
      displayName: string
    }
  }
  finalResponse: {
    content: string
    timestamp: string
  } | null
  processingError: string | null
}

const initialState: ChatState = {
  inputValue: "",
  currentQueryText: null,
  currentMessages: [],
  // WebSocket-related initial state
  currentMessageId: null,
  thinkingSteps: {},
  finalResponse: null,
  processingError: null,
}

export const processUserMessage = createAsyncThunk(
  "chat/processUserMessage",
  async (userMessageContent: string, { dispatch, getState }) => {
    const {
      addQuery,
      addMessageToQuery,
      updateMessageInQuery,
      setActiveQueryId: setGlobalActiveQueryId,
    } = await import("./queriesSlice")
    const { setChatView } = await import("./uiSlice")

    const state = getState() as RootState
    let currentActiveQueryId = state.queries.activeQueryId
    const isNewQuery = !currentActiveQueryId

    const userMessageId = uuidv4()
    const userMessage: Message = {
      id: userMessageId,
      type: "user",
      content: userMessageContent,
      timestamp: `${new Date()}`,
    }

    // If it's a new query, create it first
    if (isNewQuery) {
      currentActiveQueryId = uuidv4()
      const newQuery: Query = {
        id: currentActiveQueryId,
        title: userMessageContent.length > 40 ? userMessageContent.substring(0, 37) + "..." : userMessageContent,
        messages: [userMessage],
        createdAt: new Date(),
      }
      dispatch(addQuery(newQuery))
      dispatch(setGlobalActiveQueryId(currentActiveQueryId))
      dispatch(loadMessages(newQuery.messages))
      dispatch(setCurrentQueryText(newQuery.title))
    } else {
      // Add user message to existing query
      dispatch(addMessageToQuery({ queryId: currentActiveQueryId as string, message: userMessage }))
      // Reload messages in chatSlice to include the new user message
      const updatedQuery = (getState() as RootState).queries.queries.find((q) => q.id === currentActiveQueryId)
      if (updatedQuery) {
        dispatch(loadMessages(updatedQuery.messages))
        dispatch(setCurrentQueryText(updatedQuery.title))
      }
    }

    // Add placeholder assistant message that will be processed
    const assistantMessageId = uuidv4()
    const initialThinkingState: MessageThinkingState = {
      status: "processing",
      steps: getInitialThinkingSteps(),
      currentStepIndex: 0,
    }
    const assistantPlaceholderMessage: Message = {
      id: assistantMessageId,
      type: "assistant",
      content: "", // Empty content while processing
      timestamp: `${new Date()}`,
      thinkingState: initialThinkingState,
    }
    dispatch(addMessageToQuery({ queryId: currentActiveQueryId as string, message: assistantPlaceholderMessage }))

    // Ensure UI reflects thinking state immediately
    dispatch(setChatView("thinking"))

    // Simulate thinking process, updating the specific assistant message
    let currentSteps = [...initialThinkingState.steps]
    for (let i = 0; i < currentSteps.length; i++) {
      currentSteps = currentSteps.map((step, index) => (index === i ? { ...step, status: "in-progress" } : step))
      dispatch(
        updateMessageInQuery({
          queryId: currentActiveQueryId as string,
          messageId: assistantMessageId,
          updatedMessageData: {
            thinkingState: { ...initialThinkingState, steps: currentSteps, status: "processing", currentStepIndex: i },
          },
        }),
      )
      await new Promise((resolve) => setTimeout(resolve, 1000)) // Simulate work

      currentSteps = currentSteps.map((step, index) => (index === i ? { ...step, status: "completed" } : step))
      dispatch(
        updateMessageInQuery({
          queryId: currentActiveQueryId as string,
          messageId: assistantMessageId,
          updatedMessageData: {
            thinkingState: { ...initialThinkingState, steps: currentSteps, status: "processing", currentStepIndex: i },
          },
        }),
      )
    }

    // Final update to the assistant message with content and completed state
    const finalAssistantMessageData: Partial<Message> = {
      ...mockFinalResponse,
      thinkingState: { status: "completed", steps: currentSteps },
    }
    dispatch(
      updateMessageInQuery({
        queryId: currentActiveQueryId as string,
        messageId: assistantMessageId,
        updatedMessageData: finalAssistantMessageData,
      }),
    )

    // Update UI state after processing
    dispatch(setChatView("response"))

    return { queryId: currentActiveQueryId, isNewQuery, assistantMessageId }
  },
)

const chatSlice = createSlice({
  name: "chat",
  initialState,
  reducers: {
    setInputValue(state, action: PayloadAction<string>) {
      state.inputValue = action.payload
    },
    loadMessages(state, action: PayloadAction<Message[]>) {
      state.currentMessages = action.payload
      // Set currentQueryText from the first user message
      const firstUserMessage = action.payload.find((msg) => msg.type === "user")
      state.currentQueryText = firstUserMessage?.content || null
    },
    setCurrentQueryText(state, action: PayloadAction<string | null>) {
      state.currentQueryText = action.payload
    },
    clearInputValue(state) {
      state.inputValue = ""
    },
    startNewChat(state) {
      state.inputValue = ""
      state.currentQueryText = null
      state.currentMessages = []
      state.currentMessageId = null
      state.thinkingSteps = {}
      state.finalResponse = null
      state.processingError = null
    },
    // WebSocket-related actions
    updateThinkingStep(state, action: PayloadAction<{
      agentName: string
      status: 'idle' | 'in-progress' | 'completed'
      message: string
      result: any
      displayName: string
    }>) {
      const { agentName, status, message, result, displayName } = action.payload
      state.thinkingSteps[agentName] = {
        status,
        message,
        result,
        displayName
      }
    },
    setFinalResponse(state, action: PayloadAction<{
      content: string
      timestamp: string
    }>) {
      state.finalResponse = action.payload
    },
    setProcessingError(state, action: PayloadAction<string>) {
      state.processingError = action.payload
    },
    resetThinkingSteps(state) {
      state.thinkingSteps = {}
      state.finalResponse = null
      state.processingError = null
    },
    setCurrentMessageId(state, action: PayloadAction<string | null>) {
      state.currentMessageId = action.payload
    },
  },
})

export const {
  setInputValue,
  loadMessages,
  setCurrentQueryText,
  clearInputValue,
  startNewChat,
  updateThinkingStep,
  setFinalResponse,
  setProcessingError,
  resetThinkingSteps,
  setCurrentMessageId
} = chatSlice.actions

export default chatSlice.reducer

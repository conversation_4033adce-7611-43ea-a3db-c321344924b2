import { useSelector } from "react-redux"
import type { RootState } from "@/store"
import type { ThinkingStep as ThinkingStepType } from "@/types"
import { ThinkingStepComponent } from "./thinking-step"
import { Button } from "@/components/ui/button"

interface ThinkingProcessProps {
  currentQuery: string
  steps?: ThinkingStepType[] // Make optional for backward compatibility
}

export function ThinkingProcess({ currentQuery, steps }: ThinkingProcessProps) {
  // Get WebSocket thinking steps from Redux store
  const websocketThinkingSteps = useSelector((state: RootState) => state.chat.thinkingSteps)
  const processingError = useSelector((state: RootState) => state.chat.processingError)

  // Convert WebSocket thinking steps to ThinkingStep format
  const convertedSteps: ThinkingStepType[] = Object.entries(websocketThinkingSteps).map(([agentName, stepData]) => ({
    id: agentName,
    description: stepData.displayName,
    status: stepData.status === 'idle' ? 'pending' : stepData.status === 'in-progress' ? 'in-progress' : 'completed',
    details: stepData.message || stepData.result ? `${stepData.message}\n${JSON.stringify(stepData.result, null, 2)}` : undefined
  }))

  // Use WebSocket steps if available, otherwise fall back to provided steps
  const displaySteps = convertedSteps.length > 0 ? convertedSteps : (steps || [])

  // Check if any step is in progress
  const hasInProgressSteps = displaySteps.some((step) => step.status === "in-progress")
  return (
    <div className="space-y-6">
      {/* Tab navigation with consistent styling */}
      <div className="flex items-center gap-8 pb-4 border-b border-gray-200">
        <Button
          variant="ghost"
          className="text-teal-500 font-semibold px-0 py-2 h-auto relative after:content-[''] after:absolute after:left-0 after:bottom-[-16px] after:h-[2px] after:w-full after:bg-teal-500"
        >
          Answer
        </Button>
        <Button variant="ghost" className="text-gray-500 font-semibold px-0 py-2 h-auto hover:text-gray-700">
          Catalog
        </Button>
      </div>

      {/* Thinking steps with sources panel styling */}
      <div className="bg-gray-50 rounded-xl border border-gray-200 p-6">
        {processingError ? (
          <div className="text-red-600 p-4 bg-red-50 rounded-lg border border-red-200">
            <p className="font-medium">Processing Error:</p>
            <p className="text-sm mt-1">{processingError}</p>
          </div>
        ) : (
          <>
            <div className="space-y-4">
              {displaySteps.map((step, index) => (
                <ThinkingStepComponent key={step.id} step={step} isLastStep={index === displaySteps.length - 1} />
              ))}
            </div>

            {hasInProgressSteps && (
              <div className="mt-6 pt-4 border-t border-gray-200">
                <p className="text-base text-gray-600 animate-pulse">Generating answer...</p>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  )
}

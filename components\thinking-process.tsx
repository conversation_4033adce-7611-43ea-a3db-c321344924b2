import type { ThinkingStep as ThinkingStepType } from "@/types"
import { ThinkingStepComponent } from "./thinking-step"
import { But<PERSON> } from "@/components/ui/button"

interface ThinkingProcessProps {
  currentQuery: string
  steps: ThinkingStepType[]
}

export function ThinkingProcess({ currentQuery, steps }: ThinkingProcessProps) {
  return (
    <div className="space-y-6">
      {/* Tab navigation with consistent styling */}
      <div className="flex items-center gap-8 pb-4 border-b border-gray-200">
        <Button
          variant="ghost"
          className="text-teal-500 font-semibold px-0 py-2 h-auto relative after:content-[''] after:absolute after:left-0 after:bottom-[-16px] after:h-[2px] after:w-full after:bg-teal-500"
        >
          Answer
        </Button>
        <Button variant="ghost" className="text-gray-500 font-semibold px-0 py-2 h-auto hover:text-gray-700">
          Catalog
        </Button>
      </div>

      {/* Thinking steps with sources panel styling */}
      <div className="bg-gray-50 rounded-xl border border-gray-200 p-6">
        <div className="space-y-4">
          {steps.map((step, index) => (
            <ThinkingStepComponent key={step.id} step={step} isLastStep={index === steps.length - 1} />
          ))}
        </div>

        {steps.some((step) => step.status === "in-progress") && (
          <div className="mt-6 pt-4 border-t border-gray-200">
            <p className="text-base text-gray-600 animate-pulse">Generating answer...</p>
          </div>
        )}
      </div>
    </div>
  )
}

import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"
import { Inter } from "next/font/google"
import "./globals.css"
import { Providers } from "@/components/providers" // Import Providers

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "ROSS AI Chat App",
  description: "AI Chat Application",
    generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <Providers>
          {" "}
          {/* Wrap with Providers */}
          {children}
        </Providers>
      </body>
    </html>
  )
}

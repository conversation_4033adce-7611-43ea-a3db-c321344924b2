export type ThinkingStepStatus = "completed" | "in-progress" | "pending"

export interface ThinkingStep {
  id: string
  description: string
  status: ThinkingStepStatus
  details?: string // For accordion content
}

export interface SourceInfo {
  id: string
  name: string
  type: "database" | "linkedin" | "email" | "web" | "pdf" | "docs" | "faq" | "feedback"
  enabled: boolean // This might be more relevant for source selection panel
  url?: string // URL for the source if applicable
  snippet?: string // A small snippet from the source
}

export interface MessageThinkingState {
  status: "processing" | "completed" | "error"
  steps: ThinkingStep[]
  currentStepIndex?: number // To track which step is 'in-progress'
  errorDetails?: string
}

export interface Message {
  id: string
  type: "user" | "assistant"
  content: string // For user message, or final assistant response
  timestamp: string
  sources?: SourceInfo[] // For assistant messages, after processing
  thinkingState?: MessageThinkingState // For assistant messages, during and after processing
  responseTitle?: string // For assistant messages
}

export interface Query {
  id: string
  title: string
  messages: Message[]
  createdAt: Date
}

// For Source Selection Panel - removed icon property to fix serialization
export interface SourceConfig {
  id: string // e.g., 'rossDatabase', 'linkedin'
  name: string
  type: SourceInfo["type"]
  colorClass?: string
  enabled: boolean
}

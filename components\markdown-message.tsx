import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import type { FC } from "react";

interface MarkdownMessageProps {
  content: string;
}

export const MarkdownMessage: FC<MarkdownMessageProps> = ({ content }) => {
  return (
    <div className="prose prose-sm max-w-none text-gray-700">
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        components={{
          code({node, inline, className, children, ...props}: any) {
            return !inline ? (
              <pre className="bg-gray-300 text-black rounded-md p-3 overflow-x-auto my-3">
                <code className={className} {...props}>
                  {children}
                </code>
              </pre>
            ) : (
              <code className="bg-gray-200 text-red-700 rounded px-1">{children}</code>
            );
          },
          table({children}) {
            return (
              <table className="border border-gray-300 my-4 shadow-sm">{children}</table>
            );
          },
          th({children}) {
            return (
              <th className="bg-gray-100 border border-gray-300 px-2 py-1 font-semibold">{children}</th>
            );
          },
          td({children}) {
            return (
              <td className="border border-gray-300 px-2 py-1">{children}</td>
            );
          },
          blockquote({children}) {
            return (
              <blockquote className="border-l-4 border-blue-400 bg-blue-50 italic pl-4 pr-2 py-2 my-4">
                {children}
              </blockquote>
            );
          }
        }}
      >
        {content}
      </ReactMarkdown>
    </div>
  );
};

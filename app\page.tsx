"use client"

import { useEffect } from "react"
import { useDispatch } from "react-redux"
import type { AppDispatch } from "@/store"
import { setActiveTab, setChatView } from "@/store/uiSlice"
import { setActiveQueryId } from "@/store/queriesSlice"
import { startNewChat } from "@/store/chatSlice"
import { AppLayout } from "@/components/app-layout"

export default function Home() {
  const dispatch = useDispatch<AppDispatch>()

  useEffect(() => {
    // Set up initial state for home page (new chat)
    dispatch(setActiveTab("chat"))
    dispatch(setChatView("chat"))
    dispatch(setActiveQueryId(null))
    dispatch(startNewChat())
  }, [dispatch])

  return <AppLayout />
}

import { createSlice, type PayloadAction } from "@reduxjs/toolkit"
import type { SourceConfig } from "@/types"

export interface SourcesState {
  sources: {
    rossDatabase: { enabled: boolean; config: SourceConfig }
    linkedin: { enabled: boolean; config: SourceConfig }
    email: { enabled: boolean; config: SourceConfig }
  }
}

const initialState: SourcesState = {
  sources: {
    rossDatabase: {
      enabled: true,
      config: {
        id: "rossDatabase",
        name: "Ross Database",
        type: "database",
        colorClass: "text-gray-600",
        enabled: true,
      },
    },
    linkedin: {
      enabled: true,
      config: {
        id: "linkedin",
        name: "LinkedIn",
        type: "linkedin",
        colorClass: "text-blue-600",
        enabled: true,
      },
    },
    email: {
      enabled: true,
      config: {
        id: "email",
        name: "Email",
        type: "email",
        colorClass: "text-red-500",
        enabled: true,
      },
    },
  },
}

const sourcesSlice = createSlice({
  name: "sources",
  initialState,
  reducers: {
    toggleSource(state, action: PayloadAction<keyof SourcesState["sources"]>) {
      state.sources[action.payload].enabled = !state.sources[action.payload].enabled
    },
    setSourceEnabled(state, action: PayloadAction<{ sourceId: keyof SourcesState["sources"]; enabled: boolean }>) {
      state.sources[action.payload.sourceId].enabled = action.payload.enabled
    },
  },
})

export const { toggleSource, setSourceEnabled } = sourcesSlice.actions
export default sourcesSlice.reducer

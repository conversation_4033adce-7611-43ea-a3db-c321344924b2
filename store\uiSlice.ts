import { createSlice, type PayloadAction } from "@reduxjs/toolkit"

export type ActiveTab = "chat" | "queries"
export type ChatView = "chat" | "thinking" | "response"
export type Theme = "light" | "dark" | "system"

interface UiState {
  activeTab: ActiveTab
  chatView: ChatView
  theme: Theme
  sidebarCollapsed: boolean
}

const initialState: UiState = {
  activeTab: "chat",
  chatView: "chat",
  theme: "system",
  sidebarCollapsed: false,
}

const uiSlice = createSlice({
  name: "ui",
  initialState,
  reducers: {
    setActiveTab(state, action: PayloadAction<ActiveTab>) {
      state.activeTab = action.payload
      // When switching to chat tab, determine if it's a new chat or a loaded one.
      // This logic is getting complex, a listener middleware would be better,
      // but for now, we can reset. The loading logic will set it to 'response'.
      if (action.payload === "chat") {
        // We'll let the query loading logic decide the view.
        // If no query is active, it should be 'chat'.
      }
    },
    setChatView(state, action: PayloadAction<ChatView>) {
      state.chatView = action.payload
    },
    setTheme(state, action: PayloadAction<Theme>) {
      state.theme = action.payload
    },
    setSidebarCollapsed(state, action: PayloadAction<boolean>) {
      state.sidebarCollapsed = action.payload
    },
    toggleSidebar(state) {
      state.sidebarCollapsed = !state.sidebarCollapsed
    },
  },
})

export const { setActiveTab, setChatView, setTheme, setSidebarCollapsed, toggleSidebar } = uiSlice.actions
export default uiSlice.reducer

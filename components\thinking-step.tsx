import type React from "react"
import type { ThinkingStep, ThinkingStepStatus } from "@/types"
import { Check<PERSON>ircle2, Loader2, Circle, ChevronRight } from "lucide-react"

interface ThinkingStepProps {
  step: ThinkingStep
  isLastStep?: boolean
}

const statusIcons: Record<ThinkingStepStatus, React.ElementType> = {
  completed: CheckCircle2,
  "in-progress": Loader2,
  pending: Circle,
}

const statusColors: Record<ThinkingStepStatus, string> = {
  completed: "text-teal-500",
  "in-progress": "text-blue-500 animate-spin",
  pending: "text-gray-400",
}

export function ThinkingStepComponent({ step, isLastStep }: ThinkingStepProps) {
  const IconComponent = statusIcons[step.status]

  return (
    <div className="flex items-center justify-between py-3 px-4 rounded-lg hover:bg-white/50 transition-colors">
      <div className="flex items-center gap-4">
        <IconComponent className={`h-5 w-5 ${statusColors[step.status]} flex-shrink-0`} />
        <p
          className={`text-base font-medium ${
            step.status === "completed"
              ? "text-gray-900"
              : step.status === "in-progress"
                ? "text-blue-700"
                : "text-gray-600"
          }`}
        >
          {step.description}
          {step.status === "in-progress" && "..."}
        </p>
      </div>

      {step.status === "completed" && <ChevronRight className="h-5 w-5 text-gray-400 flex-shrink-0" />}
    </div>
  )
}

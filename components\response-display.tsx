import type { ThinkingStep } from "@/types"
import { Button } from "@/components/ui/button"
import { CompletedStepsAccordion } from "./completed-steps-accordion"
import { MarkdownMessage } from "./markdown-message"

interface ResponseDisplayProps {
  query: string
  completedSteps: ThinkingStep[]
  responseTitle: string
  responseContent: string
}

export function ResponseDisplay({ query, completedSteps, responseTitle, responseContent }: ResponseDisplayProps) {
  return (
    <div className="mt-8">
      <h2 className="text-2xl font-semibold text-gray-800 mb-3">{query}</h2>
      <div className="flex items-center gap-4 mb-6">
        <Button
          variant="ghost"
          className="text-teal-500 font-semibold px-1 py-1 h-auto relative after:content-[''] after:absolute after:left-0 after:bottom-[-2px] after:h-[2px] after:w-full after:bg-teal-500"
        >
          Answer
        </Button>
        <Button variant="ghost" className="text-gray-500 font-semibold px-1 py-1 h-auto">
          Catalog
        </Button>
        <span className="ml-auto text-sm text-gray-500">Sources</span>
      </div>

      <CompletedStepsAccordion steps={completedSteps} />

      <div className="mt-8">
        <h3 className="text-lg font-semibold text-gray-800 mb-2">{responseTitle}</h3>
        <MarkdownMessage content={responseContent} />
      </div>
    </div>
  )
}

# AI Chat App Development Task List

## Phase 1: Project Setup and Basic Structure

### 1.1 Initialize Project
- [ ] Set up React project with Vite or Create React App
- [ ] Install required dependencies:
  - Tailwind CSS for styling
  - Lucide React for icons
  - React Markdown for markdown parsing
  - UUID for generating unique IDs
- [ ] Set up project folder structure:
  ```
  src/
  ├── components/
  ├── hooks/
  ├── utils/
  ├── types/
  └── styles/
  ```

### 1.2 Define Data Models
- [ ] Create TypeScript interfaces for:
  - `Query` (id, title, messages, createdAt)
  - `Message` (id, type, content, timestamp, sources, thinking)
  - `Source` (name, type, enabled)
  - `ThinkingStep` (step, description, status)

## Phase 2: Core Layout Components

### 2.1 Main App Layout
- [ ] Create `AppLayout` component with:
  - Header section with app title
  - Tab navigation (Chat/Queries)
  - Main content area
  - Responsive design matching screenshot aesthetics

### 2.2 Tab Navigation
- [ ] Implement `TabNavigation` component:
  - Chat tab (active by default)
  - Queries tab
  - Clean, minimal styling matching screenshots
  - State management for active tab

## Phase 3: Chat Interface Components

### 3.1 Chat Header and Greeting
- [ ] Create `ChatHeader` component:
  - "Good afternoon, [Username]" greeting
  - Clean typography matching screenshots

### 3.2 Sources Selection Panel
- [ ] Build `SourcesPanel` component:
  - Toggle switches for different sources (Ross Database, LinkedIn, Email)
  - Show/hide based on query state
  - Icons and labels matching screenshot design
  - State management for enabled sources

### 3.3 Message Input
- [ ] Create `MessageInput` component:
  - Large text area with placeholder text
  - Send button with proper styling
  - Auto-resize functionality
  - Submit on Enter (with Shift+Enter for new lines)

### 3.4 Suggested Queries
- [ ] Implement `SuggestedQueries` component:
  - Display suggestion pills below input
  - Click to populate input field
  - Responsive layout for multiple suggestions

## Phase 4: Thinking UI Components

### 4.1 Thinking Process Display
- [ ] Create `ThinkingProcess` component:
  - Expandable/collapsible sections
  - Progress indicators for each step:
    - "Completed User Analysis" ✓
    - "Searching Web..." (loading)
    - "Searching Ross Database..." (loading)
    - "Searching Ross Emails, PDFs, Docs and FAQs" (loading)
    - "Searching Ross Senior Team Feedbacks" (loading)
  - "Generating answer..." status at bottom
  - Smooth animations for state changes

### 4.2 Thinking Step Component
- [ ] Build `ThinkingStep` component:
  - Icon (checkmark for completed, spinner for in-progress)
  - Step description
  - Expandable details section
  - Sources used in this step
  - Proper styling matching screenshots

## Phase 5: Message Display Components

### 5.1 Message Container
- [ ] Create `MessageContainer` component:
  - Handle different message types (user, assistant, thinking)
  - Proper spacing and layout
  - Timestamp display

### 5.2 Markdown Renderer
- [ ] Implement `MarkdownMessage` component:
  - Integrate React Markdown
  - Custom styling for headers, lists, code blocks
  - Proper typography matching the clean design
  - Support for links and formatting

### 5.3 Sources Display
- [ ] Build `SourcesDisplay` component:
  - Show sources used for each response
  - Clickable source links
  - Proper attribution formatting

## Phase 6: Queries Management

### 6.1 Queries List
- [ ] Create `QueriesList` component:
  - Display all user queries with titles
  - Show timestamp/date for each query
  - Click to switch between queries
  - Active query highlighting

### 6.2 Query Management
- [ ] Implement query management functionality:
  - Auto-generate query titles from first message
  - Save queries to state/localStorage
  - Switch between queries
  - Delete queries option

## Phase 7: State Management

### 7.1 Global State Setup
- [ ] Implement state management:
  - Current active query
  - All queries list
  - Active tab
  - Sources configuration
  - Message history per query

### 7.2 Custom Hooks
- [ ] Create custom hooks:
  - `useQueries` - manage queries CRUD operations
  - `useMessages` - handle message operations
  - `useSources` - manage source selections
  - `useThinking` - handle thinking process states

## Phase 8: Integration and Interactions

### 8.1 Message Flow
- [ ] Implement complete message flow:
  - User sends message
  - Show thinking process
  - Display AI response with markdown
  - Show sources used
  - Enable follow-up messages

### 8.2 Query Switching
- [ ] Implement query switching:
  - Save current query state
  - Load selected query
  - Maintain message history
  - Update UI accordingly

## Phase 9: Styling and Polish

### 9.1 Visual Design
- [ ] Apply consistent styling:
  - Color scheme matching screenshots
  - Typography hierarchy
  - Spacing and layout
  - Hover states and transitions
  - Loading animations

### 9.2 Responsive Design
- [ ] Ensure mobile compatibility:
  - Responsive layout adjustments
  - Touch-friendly interactions
  - Mobile-optimized spacing

## Phase 10: Testing and Optimization

### 10.1 Functionality Testing
- [ ] Test all core features:
  - Creating new queries
  - Switching between queries
  - Source selection
  - Message sending and display
  - Thinking process simulation

### 10.2 Performance Optimization
- [ ] Optimize performance:
  - Lazy loading for query history
  - Memoization for expensive operations
  - Efficient re-rendering strategies

### 10.3 Error Handling
- [ ] Implement error boundaries and handling:
  - Network error handling
  - Invalid state recovery
  - User feedback for errors

## Phase 11: Final Integration

### 11.1 Demo Data
- [ ] Create realistic demo data:
  - Sample queries and responses
  - Mock thinking processes
  - Example sources and results

### 11.2 Final Testing
- [ ] Comprehensive testing:
  - User flow testing
  - Cross-browser compatibility
  - Performance validation
  - UI/UX validation against screenshots

This task list provides a systematic approach to building the AI chat app that matches the provided screenshots, with proper component separation, state management, and user experience considerations.
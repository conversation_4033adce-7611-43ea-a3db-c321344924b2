import { MessageSquare, Search, Database } from "lucide-react"
import { Button } from "@/components/ui/button"

interface EmptyStateProps {
  type: "queries" | "thinking" | "messages"
  title: string
  description: string
  actionLabel?: string
  onAction?: () => void
}

export function EmptyState({ type, title, description, actionLabel, onAction }: EmptyStateProps) {
  const getIcon = () => {
    switch (type) {
      case "queries":
        return <Search className="h-12 w-12 text-gray-400" />
      case "thinking":
        return <Database className="h-12 w-12 text-gray-400" />
      case "messages":
        return <MessageSquare className="h-12 w-12 text-gray-400" />
      default:
        return <MessageSquare className="h-12 w-12 text-gray-400" />
    }
  }

  return (
    <div className="flex flex-col items-center justify-center py-12 px-4 text-center">
      <div className="mb-4">
        {getIcon()}
      </div>
      <h3 className="text-lg font-medium text-gray-900 mb-2">
        {title}
      </h3>
      <p className="text-sm text-gray-500 mb-6 max-w-sm">
        {description}
      </p>
      {actionLabel && onAction && (
        <Button onClick={onAction} variant="outline">
          {actionLabel}
        </Button>
      )}
    </div>
  )
}

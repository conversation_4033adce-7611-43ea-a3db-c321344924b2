import { createSlice, type PayloadAction } from "@reduxjs/toolkit"
import type { Query, Message } from "@/types"
import { v4 as uuidv4 } from "uuid"

// Mock data for past queries
const mockQueries: Query[] = [
  {
    id: "mock-query-1",
    title: "NPT-thread flow control valves",
    messages: [
      {
        id: uuidv4(),
        type: "user",
        content: "Search NPT-thread flow control valves",
        timestamp: `${new Date(Date.now() - 2 * 60 * 60 * 1000)}`,
      },
      {
        id: uuidv4(),
        type: "assistant",
        responseTitle: "Information on NPT-Thread Flow Control Valves",
        content: "We have several models available. The 500-series is our most popular for industrial applications...",
        timestamp: `${new Date(Date.now() - 2 * 60 * 60 * 1000 + 5000)}`,
        sources: [
          { id: "db-1", name: "Ross Product Catalog", type: "database", enabled: true, snippet: "Series 500, NPT..." },
        ],
        thinkingState: {
          status: "completed",
          steps: [
            { id: "1", description: "User Analysis", status: "completed" },
            { id: "2", description: "Searched Ross Database", status: "completed" },
          ],
        },
      },
    ],
    createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
  },
]

interface QueriesState {
  queries: Query[]
  activeQueryId: string | null
}

const initialState: QueriesState = {
  queries: mockQueries,
  activeQueryId: null,
}

const queriesSlice = createSlice({
  name: "queries",
  initialState,
  reducers: {
    addQuery(state, action: PayloadAction<Query>) {
      if (!state.queries.find((q) => q.id === action.payload.id)) {
        state.queries.unshift(action.payload)
      }
    },
    // Adds a message to a specific query
    addMessageToQuery(state, action: PayloadAction<{ queryId: string; message: Message }>) {
      const query = state.queries.find((q) => q.id === action.payload.queryId)
      if (query) {
        query.messages.push(action.payload.message)
      }
    },
    // Updates a specific message within a query (e.g., its thinkingState or final content)
    updateMessageInQuery(
      state,
      action: PayloadAction<{ queryId: string; messageId: string; updatedMessageData: Partial<Message> }>,
    ) {
      const query = state.queries.find((q) => q.id === action.payload.queryId)
      if (query) {
        const messageIndex = query.messages.findIndex((m) => m.id === action.payload.messageId)
        if (messageIndex !== -1) {
          query.messages[messageIndex] = { ...query.messages[messageIndex], ...action.payload.updatedMessageData }
        }
      }
    },
    deleteQuery(state, action: PayloadAction<string>) {
      state.queries = state.queries.filter((q) => q.id !== action.payload)
      if (state.activeQueryId === action.payload) {
        state.activeQueryId = null
      }
    },
    setActiveQueryId(state, action: PayloadAction<string | null>) {
      state.activeQueryId = action.payload
    },
  },
})

export const { addQuery, addMessageToQuery, updateMessageInQuery, deleteQuery, setActiveQueryId } = queriesSlice.actions
export default queriesSlice.reducer

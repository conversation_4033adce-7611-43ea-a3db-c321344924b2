import { createSlice, type PayloadAction } from "@reduxjs/toolkit"
import type { Query, Message } from "@/types"


// Removed mock data - queries will be created from real user interactions

interface QueriesState {
  queries: Query[]
  activeQueryId: string | null
}

const initialState: QueriesState = {
  queries: [],
  activeQueryId: null,
}

const queriesSlice = createSlice({
  name: "queries",
  initialState,
  reducers: {
    addQuery(state, action: PayloadAction<Query>) {
      if (!state.queries.find((q) => q.id === action.payload.id)) {
        state.queries.unshift(action.payload)
      }
    },
    // Adds a message to a specific query
    addMessageToQuery(state, action: PayloadAction<{ queryId: string; message: Message }>) {
      const query = state.queries.find((q) => q.id === action.payload.queryId)
      if (query) {
        query.messages.push(action.payload.message)
      }
    },
    // Updates a specific message within a query (e.g., its thinkingState or final content)
    updateMessageInQuery(
      state,
      action: PayloadAction<{ queryId: string; messageId: string; updatedMessageData: Partial<Message> }>,
    ) {
      const query = state.queries.find((q) => q.id === action.payload.queryId)
      if (query) {
        const messageIndex = query.messages.findIndex((m) => m.id === action.payload.messageId)
        if (messageIndex !== -1) {
          query.messages[messageIndex] = { ...query.messages[messageIndex], ...action.payload.updatedMessageData }
        }
      }
    },
    deleteQuery(state, action: PayloadAction<string>) {
      state.queries = state.queries.filter((q) => q.id !== action.payload)
      if (state.activeQueryId === action.payload) {
        state.activeQueryId = null
      }
    },
    setActiveQueryId(state, action: PayloadAction<string | null>) {
      state.activeQueryId = action.payload
    },
  },
})

export const { addQuery, addMessageToQuery, updateMessageInQuery, deleteQuery, setActiveQueryId } = queriesSlice.actions
export default queriesSlice.reducer

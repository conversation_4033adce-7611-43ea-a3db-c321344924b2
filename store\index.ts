import { configureStore } from "@reduxjs/toolkit"
import { persistStore, persistReducer, FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER } from "redux-persist"
import storage from "redux-persist/lib/storage"
import { combineReducers } from "@reduxjs/toolkit"
import uiReducer from "./uiSlice"
import sourcesReducer from "./sourcesSlice"
import chatReducer from "./chatSlice"
import queriesReducer from "./queriesSlice"
import websocketReducer from "./websocketSlice"
import { websocketMiddleware } from "./middleware/websocketMiddleware"

// Persist configurations for different slices
const uiPersistConfig = {
  key: "ui",
  storage,
  whitelist: ["activeTab", "chatView", "theme", "sidebarCollapsed"], // Persist important UI states
}

const sourcesPersistConfig = {
  key: "sources",
  storage,
  // Persist all sources configuration
}

const queriesPersistConfig = {
  key: "queries",
  storage,
  whitelist: ["queries", "activeQueryId"], // Persist queries and active query
  blacklist: [], // We can add blacklist if needed to exclude certain fields
}

// Chat slice is not persisted as it contains temporary UI state

// Create persisted reducers
const persistedUiReducer = persistReducer(uiPersistConfig, uiReducer)
const persistedSourcesReducer = persistReducer(sourcesPersistConfig, sourcesReducer)
const persistedQueriesReducer = persistReducer(queriesPersistConfig, queriesReducer)

// Combine all reducers
const rootReducer = combineReducers({
  ui: persistedUiReducer,
  sources: persistedSourcesReducer,
  chat: chatReducer, // Not persisted
  queries: persistedQueriesReducer,
  websocket: websocketReducer, // Not persisted - connection state is temporary
})

export const store = configureStore({
  reducer: rootReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [
          FLUSH,
          REHYDRATE,
          PAUSE,
          PERSIST,
          PURGE,
          REGISTER,
          "chat/addMessage",
          "queries/addQuery",
          "websocket/sendWebSocketMessage",
        ],
        ignoredActionPaths: ["payload.timestamp", "payload.createdAt", "payload.messages"],
        ignoredPaths: ["chat.currentMessages", "queries.queries", "websocket"],
      },
    }).concat(websocketMiddleware),
})

export const persistor = persistStore(store)

export type RootState = ReturnType<typeof rootReducer>
export type AppDispatch = typeof store.dispatch

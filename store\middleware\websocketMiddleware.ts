import type { Middleware } from '@reduxjs/toolkit'
import { websocketService, type IncomingMessage } from '@/services/websocketService'
import { 
  setConnectionStatus, 
  setConnectionError, 
  connectWebSocket, 
  disconnectWebSocket, 
  sendWebSocketMessage,
  incrementReconnectAttempts 
} from '../websocketSlice'
import { 
  updateThinkingStep, 
  setFinalResponse, 
  setProcessingError, 
  resetThinkingSteps 
} from '../chatSlice'
import type { RootState } from '../index'

/**
 * WebSocket middleware to handle WebSocket lifecycle and message processing
 */
export const websocketMiddleware: Middleware<{}, RootState> = (store) => {
  let isInitialized = false

  const initializeWebSocket = () => {
    if (isInitialized) return
    
    websocketService.initialize({
      onMessage: (message: IncomingMessage) => {
        handleIncomingMessage(message, store)
      },
      onConnectionChange: (status) => {
        store.dispatch(setConnectionStatus(status))
        
        if (status === 'connecting') {
          const state = store.getState()
          if (state.websocket.reconnectAttempts > 0) {
            store.dispatch(incrementReconnectAttempts())
          }
        }
      },
      onError: (error) => {
        store.dispatch(setConnectionError(error))
      }
    })
    
    isInitialized = true
  }

  return (next) => (action) => {
    // Initialize WebSocket service on first action
    if (!isInitialized) {
      initializeWebSocket()
    }

    // Handle WebSocket-specific actions
    if (connectWebSocket.match(action)) {
      websocketService.connect().catch((error) => {
        console.error('Failed to connect WebSocket:', error)
        store.dispatch(setConnectionError('Failed to connect to server'))
      })
      return next(action)
    }

    if (disconnectWebSocket.match(action)) {
      websocketService.disconnect()
      return next(action)
    }

    if (sendWebSocketMessage.match(action)) {
      const { query, sources } = action.payload
      const state = store.getState()
      
      // Check if WebSocket is connected
      if (!websocketService.isConnected()) {
        // Try to connect first
        websocketService.connect().then(() => {
          sendMessage(query, sources)
        }).catch((error) => {
          console.error('Failed to connect before sending message:', error)
          store.dispatch(setConnectionError('Failed to connect to server'))
        })
      } else {
        sendMessage(query, sources)
      }
      
      function sendMessage(query: string, sources: any) {
        const message = websocketService.formatOutgoingMessage(query, sources)
        const success = websocketService.sendMessage(message)
        
        if (success) {
          // Reset thinking steps when starting new query
          store.dispatch(resetThinkingSteps())
        } else {
          store.dispatch(setConnectionError('Failed to send message'))
        }
      }
      
      return next(action)
    }

    return next(action)
  }
}

/**
 * Handle incoming WebSocket messages and dispatch appropriate Redux actions
 */
function handleIncomingMessage(message: IncomingMessage, store: any) {
  console.log('Processing incoming WebSocket message:', message)

  // Handle error messages
  if (message.status === 'error') {
    const errorMessage = message.message || 'Unknown server error'
    store.dispatch(setProcessingError(errorMessage))
    return
  }

  // Handle agent status updates
  if (message.agent_invoked && message.agent_status) {
    const agentName = message.agent_invoked
    const status = message.agent_status
    const agentMessage = message.agent_message || ''
    const agentResult = message.agent_result || null

    // Map agent status to thinking step status
    const stepStatus = status === 'starting' ? 'in-progress' : 'completed'
    
    // Get display name for the agent
    const displayName = websocketService.getAgentDisplayName(agentName)
    
    store.dispatch(updateThinkingStep({
      agentName,
      status: stepStatus,
      message: agentMessage,
      result: agentResult,
      displayName
    }))
  }

  // Handle final response
  if (message.final_response) {
    store.dispatch(setFinalResponse({
      content: message.final_response,
      timestamp: new Date().toISOString()
    }))
  }
}

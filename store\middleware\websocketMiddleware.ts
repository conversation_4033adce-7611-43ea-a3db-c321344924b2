import type { Middleware } from '@reduxjs/toolkit'
import { websocketService, type IncomingMessage } from '@/services/websocketService'
import { 
  setConnectionStatus, 
  setConnectionError, 
  connectWebSocket, 
  disconnectWebSocket, 
  sendWebSocketMessage,
  incrementReconnectAttempts 
} from '../websocketSlice'
import {
  updateThinkingStep,
  setFinalResponse,
  setProcessingError,
  resetThinkingSteps,
  setCurrentMessageId
} from '../chatSlice'
import type { RootState } from '../index'

/**
 * WebSocket middleware to handle WebSocket lifecycle and message processing
 */
export const websocketMiddleware: Middleware<{}, RootState> = (store) => {
  let isInitialized = false

  const initializeWebSocket = () => {
    if (isInitialized) return
    
    websocketService.initialize({
      onMessage: (message: IncomingMessage) => {
        handleIncomingMessage(message, store)
      },
      onConnectionChange: (status) => {
        store.dispatch(setConnectionStatus(status))
        
        if (status === 'connecting') {
          const state = store.getState()
          if (state.websocket.reconnectAttempts > 0) {
            store.dispatch(incrementReconnectAttempts())
          }
        }
      },
      onError: (error) => {
        store.dispatch(setConnectionError(error))
      }
    })
    
    isInitialized = true
  }

  return (next) => (action) => {
    // Initialize WebSocket service on first action
    if (!isInitialized) {
      initializeWebSocket()
    }

    // Handle WebSocket-specific actions
    if (connectWebSocket.match(action)) {
      websocketService.connect().catch((error) => {
        console.error('Failed to connect WebSocket:', error)
        store.dispatch(setConnectionError('Failed to connect to server'))
      })
      return next(action)
    }

    if (disconnectWebSocket.match(action)) {
      websocketService.disconnect()
      return next(action)
    }

    if (sendWebSocketMessage.match(action)) {
      const { query, sources } = action.payload
      const state = store.getState()
      
      // Check if WebSocket is connected
      if (!websocketService.isConnected()) {
        // Try to connect first
        websocketService.connect().then(() => {
          sendMessage(query, sources)
        }).catch((error) => {
          console.error('Failed to connect before sending message:', error)
          store.dispatch(setConnectionError('Failed to connect to server'))
        })
      } else {
        sendMessage(query, sources)
      }
      
      function sendMessage(query: string, sources: any) {
        console.log('Sending WebSocket message:', query, sources)
        const message = websocketService.formatOutgoingMessage(query, sources)
        const success = websocketService.sendMessage(message)
        console.log('Send message success:', success, message)
        if (success) {
          // Reset thinking steps when starting new query
          store.dispatch(resetThinkingSteps())
        } else {
          store.dispatch(setConnectionError('Failed to send message'))
        }
      }
      
      return next(action)
    }

    return next(action)
  }
}

/**
 * Handle incoming WebSocket messages and dispatch appropriate Redux actions
 */
function handleIncomingMessage(message: IncomingMessage, store: any) {
  console.log('🔄 Processing WebSocket message:', message)

  console.log('WebSocket message received:', message)

  // Handle error messages
  if (message.status === 'error') {
    const errorMessage = message.message || 'Unknown server error'
    store.dispatch(setProcessingError(errorMessage))
    return
  }

  // Handle agent status updates
  if (message.agent_invoked && message.agent_status) {
    const agentName = message.agent_invoked
    const status = message.agent_status
    const agentMessage = message.agent_message || ''
    const agentResult = message.agent_result || null

    console.log(`🤖 Agent ${agentName} status: ${status}`)

    // Map agent status to thinking step status
    const stepStatus = status === 'starting' ? 'in-progress' : 'completed'

    // Get display name for the agent
    const displayName = websocketService.getAgentDisplayName(agentName)

    store.dispatch(updateThinkingStep({
      agentName,
      status: stepStatus,
      message: agentMessage,
      result: agentResult,
      displayName
    }))
  }

  // Handle individual agent errors
  if (message.agent_invoked && message.status === 'error') {
    const agentName = message.agent_invoked
    const errorMessage = message.message || 'Agent processing failed'
    const displayName = websocketService.getAgentDisplayName(agentName)

    store.dispatch(updateThinkingStep({
      agentName,
      status: 'error',
      message: '',
      result: null,
      displayName,
      error: errorMessage
    }))
  }

  // Handle final response
  if (message.final_response) {
    const finalContent = message.final_response
    console.log('✅ Final response received:', finalContent.substring(0, 100) + '...')

    store.dispatch(setFinalResponse({
      content: finalContent,
      timestamp: new Date().toISOString()
    }))

    // Update the message in queries slice for persistence
    updateMessageWithFinalResponse(store, finalContent)
  }

  // Handle supervisor agent completion (alternative final response)
  if (message.agent_invoked === 'supervisor_agent' && message.agent_status === 'completed') {
    const finalContent = message.agent_result || message.agent_message || ''
    console.log('✅ Supervisor agent completed with response:', finalContent.substring(0, 100) + '...')

    store.dispatch(setFinalResponse({
      content: finalContent,
      timestamp: new Date().toISOString()
    }))

    // Update the message in queries slice for persistence
    updateMessageWithFinalResponse(store, finalContent)
  }
}

/**
 * Update the current assistant message with final response content
 */
function updateMessageWithFinalResponse(store: any, content: string) {
  const state = store.getState()
  const activeQueryId = state.queries.activeQueryId

  if (activeQueryId) {
    // Import the action dynamically to avoid circular dependencies
    import('../queriesSlice').then(({ updateMessageInQuery }) => {
      const query = state.queries.queries.find((q: any) => q.id === activeQueryId)
      if (query) {
        // Find the last assistant message (should be the one being processed)
        const lastAssistantMessage = [...query.messages].reverse().find((m: any) => m.type === 'assistant')

        if (lastAssistantMessage) {
          store.dispatch(updateMessageInQuery({
            queryId: activeQueryId,
            messageId: lastAssistantMessage.id,
            updatedMessageData: {
              content: content,
              thinkingState: { status: 'completed', steps: [] },
              timestamp: new Date().toISOString()
            }
          }))
        }
      }
    })
  }
}

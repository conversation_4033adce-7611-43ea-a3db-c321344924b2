import { Accordion, AccordionContent, AccordionI<PERSON>, AccordionTrigger } from "@/components/ui/accordion"
import type { ThinkingStep, SourceInfo } from "@/types"
import { CheckCircle2, Database, LinkIcon, FileText, Users, Info } from "lucide-react"

interface CompletedStepsAccordionProps {
  steps: ThinkingStep[]
  sources?: SourceInfo[]
}

const getIconForSourceType = (type: SourceInfo["type"]) => {
  switch (type) {
    case "database":
      return <Database className="h-4 w-4 text-indigo-500 mr-3" />
    case "linkedin":
      return <Users className="h-4 w-4 text-blue-600 mr-3" />
    case "email":
      return <Users className="h-4 w-4 text-red-500 mr-3" />
    case "web":
      return <LinkIcon className="h-4 w-4 text-sky-500 mr-3" />
    case "pdf":
    case "docs":
      return <FileText className="h-4 w-4 text-amber-500 mr-3" />
    case "faq":
    case "feedback":
      return <Users className="h-4 w-4 text-emerald-500 mr-3" />
    default:
      return <Info className="h-4 w-4 text-gray-500 mr-3" />
  }
}

export function CompletedStepsAccordion({ steps, sources }: CompletedStepsAccordionProps) {
  const completedSteps = steps.filter((step) => step.status === "completed")

  if (completedSteps.length === 0 && (!sources || sources.length === 0)) {
    return null
  }

  return (
    <div className="bg-gray-50 rounded-xl border border-gray-200 py-3 px-6">
      <Accordion type="single" collapsible className="w-full">
        {completedSteps.map((step) => (
          <AccordionItem key={step.id} value={step.id} className="border-b border-gray-200 last:border-b-0">
            <AccordionTrigger className="py-4 font-medium hover:no-underline data-[state=open]:text-teal-600 group">
              <div className="flex items-center gap-3">
                <CheckCircle2 className="h-5 w-5 text-teal-500 flex-shrink-0" />
                <span className="text-gray-700 group-data-[state=open]:text-teal-600 font-medium">
                  {step.description}
                </span>
              </div>
            </AccordionTrigger>
            <AccordionContent className="pl-8 text-gray-600 pb-4 text-sm leading-relaxed">
              {step.details || "No further details for this step."}
            </AccordionContent>
          </AccordionItem>
        ))}
        {sources && sources.length > 0 && (
          <AccordionItem value="sources" className="border-b-0">
            <AccordionTrigger className="py-4 font-medium hover:no-underline data-[state=open]:text-teal-600 group">
              <div className="flex items-center gap-3">
                <LinkIcon className="h-5 w-5 text-sky-500 flex-shrink-0" />
                <span className="text-gray-700 group-data-[state=open]:text-teal-600 font-medium">
                  Sources Consulted
                </span>
              </div>
            </AccordionTrigger>
            <AccordionContent className="pl-8 text-gray-600 pb-4 space-y-3">
              {sources.map((src) => (
                <div key={src.id} className="flex items-center">
                  {getIconForSourceType(src.type)}
                  <a
                    href={src.url || "#"}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:underline font-medium"
                  >
                    {src.name}
                  </a>
                </div>
              ))}
            </AccordionContent>
          </AccordionItem>
        )}
      </Accordion>
    </div>
  )
}

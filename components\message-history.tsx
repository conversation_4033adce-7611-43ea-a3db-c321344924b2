"use client"

import { useSelector } from "react-redux"
import type { RootState } from "@/store"
import type { Message } from "@/types"
import { MarkdownMessage } from "./markdown-message"
import { Use<PERSON>, <PERSON><PERSON> } from "lucide-react"
import { ThinkingProcess } from "./thinking-process"
import { CompletedStepsAccordion } from "./completed-steps-accordion"

interface MessageHistoryProps {
  messages: Message[]
}

export function MessageHistory({ messages }: MessageHistoryProps) {
  // Get WebSocket state for real-time thinking process
  const websocketThinkingSteps = useSelector((state: RootState) => state.chat.thinkingSteps)
  const finalResponse = useSelector((state: RootState) => state.chat.finalResponse)
  const processingError = useSelector((state: RootState) => state.chat.processingError)

  // Check if we have active WebSocket thinking steps
  const hasActiveThinking = Object.keys(websocketThinkingSteps).length > 0
  const hasInProgressSteps = Object.values(websocketThinkingSteps).some(step => step.status === 'in-progress')

  if (!messages || messages.length === 0) {
    return null
  }

  // Get the last user message for context
  const lastUserMessage = messages.findLast((m) => m.type === "user")?.content || "Processing..."

  return (
    <div className="space-y-12">
      {messages.map((message) => (
        <div key={message.id} className="flex gap-6 justify-start">
          {/* Avatar for both user and assistant - always on left */}
          <div
            className={`flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center mt-2 ${message.type === "user" ? "bg-blue-100" : "bg-teal-100"
              }`}
          >
            {message.type === "user" ? (
              <User className="h-5 w-5 text-blue-600" />
            ) : (
              <Bot className="h-5 w-5 text-teal-600" />
            )}
          </div>

          {/* Message content - always left aligned */}
          <div
            className={`w-[90%] rounded-2xl shadow-sm ${message.type === "user" ? "bg-blue-50 border border-blue-200 px-3 py-2" : "bg-white border border-gray-200 px-4 py-3"
              }`}
          >
            {message.type === "user" ? (
              <p className="text-base text-bold leading-relaxed text-gray-900">{message.content}</p>
            ) : (
              <div className="space-y-3">
                {/* Show WebSocket thinking process if active, otherwise fall back to message thinking state */}
                {hasActiveThinking ? (
                  <ThinkingProcess
                    currentQuery={lastUserMessage}
                  />
                ) : message.thinkingState?.status === "processing" && message.thinkingState.steps ? (
                  <ThinkingProcess
                    currentQuery={
                      messages.findLast((m) => m.type === "user" && m.timestamp < message.timestamp)?.content ||
                      "Processing..."
                    }
                    steps={message.thinkingState.steps}
                  />
                ) : null}

                {/* Show final response from WebSocket or message content */}
                {finalResponse && !hasInProgressSteps ? (
                  <div className="space-y-3">
                    <div className="prose prose-gray max-w-none">
                      <MarkdownMessage content={finalResponse.content} />
                    </div>
                  </div>
                ) : message.thinkingState?.status === "completed" ? (
                  <div className="space-y-3">
                    {/* Sources first, then content */}
                    {message.thinkingState.steps && message.thinkingState.steps.length > 0 && (
                      <div>
                        <CompletedStepsAccordion steps={message.thinkingState.steps} sources={message.sources} />
                      </div>
                    )}

                    {/* Response content below sources */}
                    {message.responseTitle && (
                      <h3 className="text-xl font-semibold text-gray-900 leading-relaxed pt-4">
                        {message.responseTitle}
                      </h3>
                    )}
                    <div className="prose prose-gray max-w-none">
                      <MarkdownMessage content={message.content} />
                    </div>
                  </div>
                ) : !message.thinkingState && message.content ? (
                  <div className="space-y-6">
                    {/* Sources first for non-thinking messages too */}
                    {message.sources && message.sources.length > 0 && (
                      <div className="pb-4 border-b border-gray-100">
                        <h5 className="text-sm font-semibold text-gray-700 mb-3">Sources:</h5>
                        <ul className="space-y-2">
                          {message.sources.map((src) => (
                            <li key={src.id} className="text-sm text-blue-600 hover:underline">
                              <a href={src.url || "#"} target="_blank" rel="noopener noreferrer">
                                {src.name}
                              </a>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}

                    {/* Content below sources */}
                    {message.responseTitle && (
                      <h3 className="text-xl font-semibold text-gray-900 leading-relaxed">{message.responseTitle}</h3>
                    )}
                    <div className="prose prose-gray max-w-none">
                      <MarkdownMessage content={message.content} />
                    </div>
                  </div>
                ) : null}
              </div>
            )}
          </div>
        </div>
      ))}
    </div>
  )
}

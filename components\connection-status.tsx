"use client"

import { useSelector, useDispatch } from "react-redux"
import { AlertCircle, CheckCircle, Loader2, WifiOff, Refresh<PERSON>w } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Alert, AlertDescription } from "@/components/ui/alert"
import type { RootState, AppDispatch } from "@/store"
import { connectWebSocket, clearError, selectConnectionStatus, selectConnectionError, selectIsReconnecting } from "@/store/websocketSlice"

export function ConnectionStatus() {
  const dispatch = useDispatch<AppDispatch>()
  const connectionStatus = useSelector(selectConnectionStatus)
  const connectionError = useSelector(selectConnectionError)
  const isReconnecting = useSelector(selectIsReconnecting)

  const handleRetry = () => {
    dispatch(clearError())
    dispatch(connectWebSocket())
  }

  // Don't show anything if connected and no errors
  if (connectionStatus === 'connected' && !connectionError) {
    return null
  }

  const getStatusIcon = () => {
    switch (connectionStatus) {
      case 'connected':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'connecting':
        return <Loader2 className="h-4 w-4 text-blue-600 animate-spin" />
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-600" />
      case 'disconnected':
        return <WifiOff className="h-4 w-4 text-gray-600" />
      default:
        return <WifiOff className="h-4 w-4 text-gray-600" />
    }
  }

  const getStatusText = () => {
    if (isReconnecting) {
      return "Reconnecting to server..."
    }
    
    switch (connectionStatus) {
      case 'connected':
        return "Connected to server"
      case 'connecting':
        return "Connecting to server..."
      case 'error':
        return connectionError || "Connection error"
      case 'disconnected':
        return "Disconnected from server"
      default:
        return "Unknown connection status"
    }
  }

  const getAlertVariant = () => {
    switch (connectionStatus) {
      case 'connected':
        return 'default'
      case 'connecting':
        return 'default'
      case 'error':
        return 'destructive'
      case 'disconnected':
        return 'default'
      default:
        return 'default'
    }
  }

  return (
    <Alert variant={getAlertVariant() as any} className="mb-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          {getStatusIcon()}
          <AlertDescription className="mb-0">
            {getStatusText()}
          </AlertDescription>
        </div>
        
        {(connectionStatus === 'error' || connectionStatus === 'disconnected') && (
          <Button
            variant="outline"
            size="sm"
            onClick={handleRetry}
            disabled={connectionStatus === 'connecting' || isReconnecting}
            className="ml-4"
          >
            <RefreshCw className={`h-3 w-3 mr-1 ${(connectionStatus === 'connecting' || isReconnecting) ? 'animate-spin' : ''}`} />
            Retry
          </Button>
        )}
      </div>
    </Alert>
  )
}
